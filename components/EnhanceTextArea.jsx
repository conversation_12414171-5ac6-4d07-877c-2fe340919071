chunk-AUZ3RYOM.js?v=56f16517:18 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
App.jsx:163 [AppContent] 显示 LoadingComponent - 登录状态检查中或状态未确定
App.jsx:163 [AppContent] 显示 LoadingComponent - 登录状态检查中或状态未确定
App.jsx:152 [AppContent] Cleanup called, current path: /article/c5e64109-fcf7-48ca-9a3b-0be6ed093159/edit
App.jsx:163 [AppContent] 显示 LoadingComponent - 登录状态检查中或状态未确定
App.jsx:163 [AppContent] 显示 LoadingComponent - 登录状态检查中或状态未确定
ApiButton.jsx:10 [ApiButton] 组件正在渲染
ApiButton.jsx:10 [ApiButton] 组件正在渲染
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
user.js:224 [UserStore] fetchUserArticles: Skipping duplicate request - already loading or fetched (forceRefresh: false )
ApiButton.jsx:10 [ApiButton] 组件正在渲染
ApiButton.jsx:10 [ApiButton] 组件正在渲染
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
App.jsx:115 [App] Bootstrap data loaded successfully
ArticleEditPage.jsx:1662 [getChunkLinePosition] Called with chunkIndex: 0, isTranslated: false
ArticleEditPage.jsx:1663 [getChunkLinePosition] Total lines in full text: 55
ArticleEditPage.jsx:1664 [getChunkLinePosition] Total chunks: 11
ArticleEditPage.jsx:1707 [getChunkLinePosition] Target chunk 0 should be at line: 1
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
EnhanceTextArea.jsx:193 [SearchPanel] isVisible changed to: false
EnhanceTextArea.jsx:657 [EnhanceTextArea Main] isSearchVisible state is NOW: false
EnhanceTextArea.jsx:193 [SearchPanel] isVisible changed to: false
EnhanceTextArea.jsx:657 [EnhanceTextArea Main] isSearchVisible state is NOW: false
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ChunkRenderer.jsx:159 [MergedEditDisplay DEBUG] Received scrollToLine event: {line: 1, eventType: 'original', keywords: 'Folate receptor alpha as a successful biomarker in', chunkIndex: 0, type: 'original'}
ChunkRenderer.jsx:147 [MergedEditDisplay DEBUG] verifyPositionAccuracy - Result: {isAccurate: true, bestMatch: null, searchText: '<h1>Folate receptor alpha as a successful biomarke…ients using preclinical and clinical models</h1> '}
ChunkRenderer.jsx:197 [MergedEditDisplay DEBUG] Dispatching scroll. Final target line: 1 Initial line was: 1 Pos: 0
ChunkRenderer.jsx:223 [MergedEditDisplay DEBUG] CodeMirror actual new caret position - Line Number: 1 Line Text: <h1>Folate receptor alpha as a successful biomarker in the treatment of low grade serous ovarian cancer patients using preclinical and clinical models</h1>
ChunkRenderer.jsx:234 [MergedEditDisplay DEBUG] After animation frame - Final Caret Line: 1 Text: <h1>Folate receptor alpha as a successful biomarker in the treatment of low grade serous ovarian cancer patients using preclinical and clinical models</h1>
ChunkRenderer.jsx:235 [MergedEditDisplay DEBUG] After animation frame - Visible Ranges: [{"from":0,"to":3840,"fromLine":1,"toLine":18}]
ChunkRenderer.jsx:238 [MergedEditDisplay DEBUG] After animation frame - Top line in view: 1
ChunkRenderer.jsx:241 [MergedEditDisplay DEBUG] CM scrollDOM.scrollTop: 0
ChunkRenderer.jsx:242 [MergedEditDisplay DEBUG] CM scrollDOM.scrollHeight: 8414
ChunkRenderer.jsx:243 [MergedEditDisplay DEBUG] CM dom.clientHeight: 8414
ChunkRenderer.jsx:244 [MergedEditDisplay DEBUG] CM contentHeight: 8414.4375
ChunkRenderer.jsx:251 [MergedEditDisplay DEBUG] CM Line 33 info: from= 8725 length= 67 text= <img src="/api/file/1d2e3061-7...
ChunkRenderer.jsx:252 [MergedEditDisplay DEBUG] CM Line 33 block: top= 3252.6992077913796 height= 38.437898495281814
ChunkRenderer.jsx:286 [MergedEditDisplay DEBUG] Body Scroll Attempt:
                         Target CM Line: 1
                         Line Top in CM Content: 0
                         CM Editor Viewport Top: 225.3125
                         Current Body ScrollTop: 0
                         Toolbar Height: 60
                         Current Target Line Absolute Y: 225.3125
                         Desired Target Line Viewport Y: 120
                         New Body ScrollTop: 105.3125
ChunkRenderer.jsx:298 [MergedEditDisplay DEBUG] Executed document.body.scrollTo: 105.3125
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
ArticleEditPage.jsx:2135 [ArticleEditPage] 调试信息: {currentUuidFromParams: 'c5e64109-fcf7-48ca-9a3b-0be6ed093159', dataFetchingLoading: false, articleFromStore: {…}, isTrulyLoading: false, hasArticleContent: true, …}
EnhanceTextArea.jsx:741 [EnhanceTextArea Main] Mod-f pressed. Current isSearchVisible: false
EnhanceTextArea.jsx:742 [EnhanceTextArea Main] Setting isSearchVisible to true.
EnhanceTextArea.jsx:744 [EnhanceTextArea Main] setIsSearchVisible called.
EnhanceTextArea.jsx:193 [SearchPanel] isVisible changed to: true
EnhanceTextArea.jsx:195 [SearchPanel] Panel becoming visible, focusing input and updating position
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 113.1015625, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '123.1015625px', right: '433.5px'}
EnhanceTextArea.jsx:16 [getScrollableParent V5] Starting for element: <div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​ Style overflow: hidden, hidden, hidden
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div style=​"position:​ relative;​ height:​ auto;​ min-height:​ 100vh;​">​…​</div>​<div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div class=​"css-dev-only-do-not-override-o8vy8x ant-app">​…​</div>​<div style=​"position:​ relative;​ height:​ auto;​ min-height:​ 100vh;​">​…​</div>​<div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​</div>​<style jsx=​"true" global=​"true">​…​</style>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <div id=​"root">​…​</div>​<div class=​"css-dev-only-do-not-override-o8vy8x ant-app">​…​</div>​<div style=​"position:​ relative;​ height:​ auto;​ min-height:​ 100vh;​">​…​</div>​<div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​</div>​<style jsx=​"true" global=​"true">​…​</style>​</div>​</div>​ Style overflow: visible, visible, visible
EnhanceTextArea.jsx:25 [getScrollableParent V5] Checking element: <body>​…​</body>​scroll<div id=​"root">​…​</div>​<div class=​"css-dev-only-do-not-override-o8vy8x ant-app">​…​</div>​<div style=​"position:​ relative;​ height:​ auto;​ min-height:​ 100vh;​">​…​</div>​<div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​</div>​<style jsx=​"true" global=​"true">​…​</style>​</div>​</div>​<script type=​"module" src=​"/​main.jsx">​</script>​<div class=​"ant-message ant-message-top css-dev-only-do-not-override-o8vy8x" style=​"left:​ 50%;​ transform:​ translateX(-50%)​;​ top:​ 8px;​">​…​</div>​</body>​ Style overflow: hidden auto, auto, hidden
EnhanceTextArea.jsx:29 [getScrollableParent V5] Found specific scrollable element: <body>​…​</body>​scroll<div id=​"root">​…​</div>​<div class=​"css-dev-only-do-not-override-o8vy8x ant-app">​…​</div>​<div style=​"position:​ relative;​ height:​ auto;​ min-height:​ 100vh;​">​…​</div>​<div class=​"ant-layout ant-layout-has-sider css-dev-only-do-not-override-o8vy8x" style=​"min-height:​ 100vh;​">​…​</div>​flex<aside class=​"ant-layout-sider ant-layout-sider-dark css-dev-only-do-not-override-o8vy8x" style=​"background:​ rgb(245, 245, 245)​;​ border-right:​ 1px solid rgb(232, 232, 232)​;​ display:​ flex;​ flex-direction:​ column;​ height:​ 100vh;​ position:​ fixed;​ left:​ 0px;​ top:​ 0px;​ bottom:​ 0px;​ z-index:​ 1000;​ flex:​ 0 0 250px;​ max-width:​ 250px;​ min-width:​ 250px;​ width:​ 250px;​">​…​</aside>​flex<div class=​"ant-layout site-layout css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ 250px;​ background:​ rgb(255, 255, 255)​;​ overflow:​ visible;​">​…​</div>​flex<div class=​"article-edit-container" style=​"padding-top:​ var(--toolbar-height, 80px)​;​ padding-right:​ ;​ padding-bottom:​ ;​ padding-left:​ ;​ --toolbar-height:​ 60px;​">​…​</div>​flex<div>​…​</div>​<div class=​"ant-row ant-row-space-between ant-row-middle article-controls toolbar-no-border css-dev-only-do-not-override-o8vy8x" style=​"position:​ fixed;​ top:​ 0px;​ left:​ 250px;​ right:​ 0px;​ z-index:​ 1000;​ background-color:​ rgb(245, 245, 245)​;​ padding:​ 16px 24px 6px;​ border-bottom:​ none;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ center;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-divider css-dev-only-do-not-override-o8vy8x ant-divider-horizontal" role=​"separator" style=​"margin:​ 16px 0px;​">​</div>​flex<div class=​"sticky-header-wrapper is-actually-sticky">​…​</div>​<div class=​"ant-space css-dev-only-do-not-override-o8vy8x ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small t-content" style=​"width:​ 100%;​">​…​</div>​flex<div class=​"ant-space-item">​…​</div>​<div id=​"chunk-row-0" class=​"ant-row chunk-row css-dev-only-do-not-override-o8vy8x" style=​"margin-left:​ -8px;​ margin-right:​ -8px;​">​…​</div>​flex<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div style=​"display:​ flex;​ flex-direction:​ column;​ width:​ 100%;​ max-width:​ 100%;​ overflow:​ hidden;​">​…​</div>​flex<div style=​"position:​ relative;​ width:​ 100%;​">​…​</div>​<div class=​"cm-theme-light" style=​"border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ overflow:​ visible;​ width:​ 100%;​ max-width:​ 100%;​ box-sizing:​ border-box;​ font-size:​ 13px;​ line-height:​ 1.6;​ resize:​ none;​">​…​</div>​<div style=​"position:​ fixed;​ top:​ 123.102px;​ right:​ 433.5px;​ background:​ white;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 6px;​ padding:​ 8px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ z-index:​ 1001;​ min-width:​ 280px;​ font-size:​ 12px;​ max-height:​ 200px;​ overflow-y:​ auto;​">​…​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 4px;​ margin-bottom:​ 4px;​">​…​</div>​flex<input placeholder=​"搜索..." type=​"text" value style=​"flex:​ 1 1 0%;​ padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ font-size:​ 12px;​">​<button disabled title=​"上一个 (Shift+Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↑​</button>​<button disabled title=​"下一个 (Enter)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​↓​</button>​<button title=​"关闭 (Esc)​" style=​"padding:​ 4px 6px;​ border:​ 1px solid rgb(217, 217, 217)​;​ border-radius:​ 4px;​ background:​ white;​ cursor:​ pointer;​ font-size:​ 12px;​">​✕​</button>​</div>​<div style=​"display:​ flex;​ align-items:​ center;​ gap:​ 8px;​ font-size:​ 11px;​ color:​ rgb(102, 102, 102)​;​">​…​</div>​flex<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"区分大小写"</label>​<label style=​"display:​ flex;​ align-items:​ center;​ gap:​ 2px;​">​…​</label>​flex<input type=​"checkbox" style=​"margin:​ 0px;​">​"正则表达式"</label>​<span style=​"margin-left:​ auto;​">​无匹配​</span>​</div>​</div>​</div>​<div style=​"margin-top:​ 10px;​ display:​ flex;​ justify-content:​ flex-end;​ visibility:​ hidden;​">​…​</div>​flex<div style=​"position:​ fixed;​ bottom:​ 10px;​ right:​ 432.5px;​ z-index:​ 1001;​ background-color:​ rgb(255, 255, 255)​;​ padding:​ 10px;​ border-radius:​ 6px;​ box-shadow:​ rgba(0, 0, 0, 0.15)​ 0px 4px 12px;​ border:​ 1px solid rgb(217, 217, 217)​;​">​…​</div>​</div>​</div>​<div class=​"ant-col ant-col-12 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​ display:​ flex;​">​…​</div>​flex<div class=​"ant-col ant-col-24 css-dev-only-do-not-override-o8vy8x" style=​"padding-left:​ 8px;​ padding-right:​ 8px;​">​…​</div>​</div>​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​<div class=​"ant-space-item">​</div>​</div>​<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex<div class=​"ant-row css-dev-only-do-not-override-o8vy8x" style=​"margin:​ 10px 0px;​ align-items:​ flex-start;​">​…​</div>​flex</div>​</div>​</div>​</div>​</div>​<style jsx=​"true" global=​"true">​…​</style>​</div>​</div>​<script type=​"module" src=​"/​main.jsx">​</script>​<div class=​"ant-message ant-message-top css-dev-only-do-not-override-o8vy8x" style=​"left:​ 50%;​ transform:​ translateX(-50%)​;​ top:​ 8px;​">​…​</div>​</body>​
EnhanceTextArea.jsx:657 [EnhanceTextArea Main] isSearchVisible state is NOW: true
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 112.1015625, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '122.1015625px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 108.1015625, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '118.1015625px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 93.1015625, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '103.1015625px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 62.1015625, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '72.1015625px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
EnhanceTextArea.jsx:187 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
EnhanceTextArea.jsx:100 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
EnhanceTextArea.jsx:136 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
EnhanceTextArea.jsx:151 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
 [SearchPanel] Toolbar found: {position: 'fixed', height: 60, bottom: 60, rect: DOMRect}
 [SearchPanel] Position calculation: {editorRect: DOMRect, obstructedHeight: 60, viewportHeight: 926, viewportWidth: 1063}
 [SearchPanel] Visible area: {visibleTop: 60, visibleRight: 639.5, visibleBottom: 926, visibleLeft: 275}
 [SearchPanel] Final position: {top: '70px', right: '433.5px'}
