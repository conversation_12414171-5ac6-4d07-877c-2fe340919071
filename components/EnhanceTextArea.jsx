import React, { useRef, useCallback, useState, useEffect } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { EditorView, lineNumbers, Decoration, keymap } from '@codemirror/view'; // Import lineNumbers and EditorView
import { StateField, StateEffect } from '@codemirror/state';

// This is the CURRENT content in the file (the SEARCH part)
// It was the result of the successful apply_diff from two turns ago
// (before my failed apply_diff attempt)
// Start line: 6, End Line: 59 (approx, based on previous successful read)

const getScrollableParent = (element) => {
  if (!element || typeof window === 'undefined') { // Removed parentNode check, element itself could be body/html
    console.log('[getScrollableParent V5] Early exit: no element or no window.');
    return null;
  }
  console.log('[getScrollableParent V5] Starting for element:', element);

  const overflowRegex = /(auto|scroll)/;
  let el = element;

  // Check element itself first, then traverse upwards
  // Loop until el is null, or we hit document (parentNode of documentElement is document)
  while (el && el.parentNode && el.nodeType === Node.ELEMENT_NODE) {
    const style = window.getComputedStyle(el);
    console.log(`[getScrollableParent V5] Checking element:`, el, `Style overflow: ${style.overflow}, ${style.overflowY}, ${style.overflowX}`);
    
    if (overflowRegex.test(style.overflow + style.overflowY + style.overflowX)) {
      if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
        console.log('[getScrollableParent V5] Found specific scrollable element:', el);
        // Do not standardize body/documentElement here, return them directly
        return el;
      }
    }
    // Optimization: if element is body or html, and not scrollable, no need to check its parents for page scroll.
    if (el === document.body || el === document.documentElement) {
        console.log('[getScrollableParent V5] Reached body/documentElement and it was not scrollable by previous check. Breaking loop.');
        break;
    }
    el = el.parentNode;
  }

  console.log('[getScrollableParent V5] Loop finished or broke. Final element in loop (or initial if no loop):', el);

  // If loop finished without returning, or if initial element was body/html and not scrollable:
  // Explicitly check body and then documentElement if they haven't been returned yet.
  // This handles cases where the scroll is on body/html directly.

  // Check document.body
  // Ensure 'el' isn't already body and returned, or that we didn't check it if it was the start.
  // The loop condition `el !== document.body` means if `el` becomes `body`, loop terminates.
  // So, if `el` is `body` here, it means it was the starting element or became `body` and wasn't scrollable in loop.
  if (el === document.body || element === document.body) { // If el is body, or was the starting element
    const bodyStyle = window.getComputedStyle(document.body);
    console.log(`[getScrollableParent V5] Explicitly checking document.body. Style overflow: ${bodyStyle.overflow}, ${bodyStyle.overflowY}, ${bodyStyle.overflowX}`);
    if (overflowRegex.test(bodyStyle.overflow + bodyStyle.overflowY + bodyStyle.overflowX)) {
      if (document.body.scrollHeight > document.body.clientHeight || document.body.scrollWidth > document.body.clientWidth) {
        console.log('[getScrollableParent V5] document.body is scrollable, returning document.body.');
        return document.body;
      }
    }
  }
  
  // Check document.documentElement
  if (el === document.documentElement || element === document.documentElement) { // If el is html, or was the starting element
    const docElemStyle = window.getComputedStyle(document.documentElement);
    console.log(`[getScrollableParent V5] Explicitly checking document.documentElement. Style overflow: ${docElemStyle.overflow}, ${docElemStyle.overflowY}, ${docElemStyle.overflowX}`);
    if (overflowRegex.test(docElemStyle.overflow + docElemStyle.overflowY + docElemStyle.overflowX)) {
      if (document.documentElement.scrollHeight > document.documentElement.clientHeight || document.documentElement.scrollWidth > document.documentElement.clientWidth) {
        console.log('[getScrollableParent V5] document.documentElement is scrollable, returning document.documentElement.');
        return document.documentElement;
      }
    }
  }
  
  console.warn('[getScrollableParent V5] No scrollable ancestor explicitly found including body/html checks, returning null. Caller should handle fallback.');
  return null;
};


// 搜索面板组件
const SearchPanel = ({ isVisible, onClose, editorRef, containerRef }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [currentMatch, setCurrentMatch] = useState(0);
  const [totalMatches, setTotalMatches] = useState(0);
  const [fixedPosition, setFixedPosition] = useState({ top: '20px', right: '20px' });
  const searchInputRef = useRef(null);
  const actualPanelRef = useRef(null); // Ref for the panel's div to get its height

  // 简化版本：计算textarea被遮挡的高度
  const calculateObstructedHeight = useCallback(() => {
    try {
      // 检查常见的工具栏元素
      const toolbarElement = document.querySelector('.article-controls');
      if (toolbarElement) {
        const toolbarRect = toolbarElement.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(toolbarElement);

        console.log('[SearchPanel] Toolbar found:', {
          position: computedStyle.position,
          height: toolbarRect.height,
          bottom: toolbarRect.bottom,
          rect: toolbarRect
        });

        if (computedStyle.position === 'fixed' || computedStyle.position === 'sticky') {
          // 返回工具栏的底部位置，这是被遮挡的高度
          return Math.max(0, toolbarRect.bottom);
        }
      } else {
        console.log('[SearchPanel] No toolbar found');
      }

      return 0;
    } catch (error) {
      console.warn('[SearchPanel] Error calculating obstructed height:', error);
      return 0;
    }
  }, []);

  // 计算搜索面板的固定位置
  const updatePanelPosition = useCallback(() => {
    if (!isVisible) return;
    if (!containerRef?.current || !editorRef?.current?.view) {
      return;
    }

    const view = editorRef.current.view;
    const editorRect = view.dom.getBoundingClientRect(); // CodeMirror编辑器的位置
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 动态计算被遮挡的高度
    const obstructedHeight = calculateObstructedHeight();
    console.log('[SearchPanel] Position calculation:', {
      editorRect,
      obstructedHeight,
      viewportHeight,
      viewportWidth
    });

    // 计算编辑器真正的可视区域
    // 如果编辑器顶部被遮挡，则从遮挡结束的位置开始
    const actualVisibleTop = Math.max(editorRect.top, obstructedHeight);
    const visibleTop = Math.max(actualVisibleTop, 0); // 确保不超出视口顶部
    const visibleRight = Math.min(editorRect.right, viewportWidth); // 确保不超出视口右侧
    const visibleBottom = Math.min(editorRect.bottom, viewportHeight); // 编辑器底部位置
    const visibleLeft = Math.max(editorRect.left, 0); // 确保不超出视口左侧

    console.log('[SearchPanel] Visible area:', {
      visibleTop,
      visibleRight,
      visibleBottom,
      visibleLeft,
      editorTop: editorRect.top,
      editorRight: editorRect.right
    });

    // 如果编辑器完全不可见，则隐藏搜索框
    if (visibleTop >= visibleBottom || visibleRight <= visibleLeft) {
      setFixedPosition({ top: '-1000px', right: '-1000px' }); // 移到屏幕外
      return;
    }

    // 计算搜索框的尺寸（估算）
    const panelWidth = 280; // 搜索框的最小宽度
    const panelHeight = 80; // 搜索框的估算高度

    // 修正位置计算：始终相对于编辑器的实际边框定位
    // 不管编辑器是否被遮挡，都保持相对于编辑器边框的固定距离
    const SEARCH_MARGIN = 15; // 统一的间距
    let finalTop = editorRect.top + SEARCH_MARGIN; // 距离编辑器实际顶部边框15px
    let finalRight = viewportWidth - editorRect.right + SEARCH_MARGIN; // 距离编辑器右边15px

    // 但是要确保搜索框不会被工具栏遮挡
    if (finalTop < obstructedHeight) {
      finalTop = obstructedHeight + SEARCH_MARGIN; // 如果会被遮挡，则放在工具栏下方，保持相同的15px间距
    }

    // 如果搜索框会超出编辑器右边界，调整位置
    if (finalRight + panelWidth > viewportWidth - visibleLeft) {
      finalRight = Math.max(10, viewportWidth - visibleLeft - panelWidth - 10);
    }

    // 如果搜索框会超出编辑器下边界，调整位置
    if (finalTop + panelHeight > visibleBottom) {
      finalTop = Math.max(visibleTop + 10, visibleBottom - panelHeight - 10);
    }

    const newPosition = {
      top: `${finalTop}px`,
      right: `${finalRight}px`
    };

    console.log('[SearchPanel] Final position:', newPosition);
    setFixedPosition(newPosition);
  }, [isVisible, editorRef, containerRef, calculateObstructedHeight]);

  // 当面板显示时聚焦搜索框并更新位置
  useEffect(() => {
    console.log('[SearchPanel] isVisible changed to:', isVisible);
    if (isVisible) {
      console.log('[SearchPanel] Panel becoming visible, focusing input and updating position');
      if (searchInputRef.current) {
        searchInputRef.current.focus({ preventScroll: true });
      }
      updatePanelPosition(); // Update position when becoming visible
    }
  }, [isVisible, updatePanelPosition]);

  // 监听滚动事件来更新面板位置
  useEffect(() => {
    if (!isVisible) {
      return;
    }

    const handleScroll = () => {
      updatePanelPosition();
    };

    const handleResize = () => {
      updatePanelPosition();
    };

    // 监听页面滚动
    let pageScrollTarget = null;
    if (containerRef.current) {
      pageScrollTarget = getScrollableParent(containerRef.current);
      if (!pageScrollTarget) {
        pageScrollTarget = document.body;
      }
    } else {
      pageScrollTarget = window;
    }

    // 监听编辑器内部滚动
    const editorView = editorRef?.current?.view;
    let editorScrollTarget = null;
    if (editorView) {
      editorScrollTarget = editorView.scrollDOM;
    }

    // 添加事件监听器
    if (pageScrollTarget) {
      pageScrollTarget.addEventListener('scroll', handleScroll, { passive: true });
    }
    if (editorScrollTarget) {
      editorScrollTarget.addEventListener('scroll', handleScroll, { passive: true });
    }
    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      // 清理事件监听器
      if (pageScrollTarget) {
        pageScrollTarget.removeEventListener('scroll', handleScroll);
      }
      if (editorScrollTarget) {
        editorScrollTarget.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [isVisible, editorRef, containerRef, updatePanelPosition]);

  // 执行搜索
  const performSearch = useCallback((term, direction = 'next') => {
    if (!editorRef.current || !term) {
      setCurrentMatch(0);
      setTotalMatches(0);
      // 清除高亮
      const view = editorRef.current?.view;
      if (view) {
        view.dispatch({
          effects: clearSearchHighlights.of(null)
        });
      }
      return;
    }

    const view = editorRef.current.view;
    if (!view) return;

    try {
      // 手动实现搜索逻辑
      const doc = view.state.doc;
      const text = doc.toString();

      let regex;
      if (useRegex) {
        try {
          regex = new RegExp(term, caseSensitive ? 'g' : 'gi');
        } catch (e) {
          // 无效的正则表达式
          setCurrentMatch(0);
          setTotalMatches(0);
          view.dispatch({
            effects: clearSearchHighlights.of(null)
          });
          return;
        }
      } else {
        const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        regex = new RegExp(escapedTerm, caseSensitive ? 'g' : 'gi');
      }

      const matches = [...text.matchAll(regex)];
      setTotalMatches(matches.length);

      if (matches.length > 0) {
        // 找到当前光标位置
        const currentPos = view.state.selection.main.head;

        // 找到最接近的匹配项
        let targetIndex = 0;
        if (direction === 'next') {
          // 如果是初始搜索（没有方向指定），从第一个匹配项开始
          if (direction === 'next' && currentPos === 0) {
            targetIndex = 0;
          } else {
            targetIndex = matches.findIndex(match => match.index > currentPos);
            if (targetIndex === -1) targetIndex = 0; // 循环到开头
          }
        } else if (direction === 'prev') {
          // 找到当前位置之前的最后一个匹配项
          const reversedMatches = matches.slice().reverse();
          const reversedIndex = reversedMatches.findIndex(match => match.index < currentPos);
          if (reversedIndex === -1) {
            targetIndex = matches.length - 1; // 循环到结尾
          } else {
            targetIndex = matches.length - 1 - reversedIndex;
          }
        } else {
          // 默认情况（初始搜索），选择第一个匹配项
          targetIndex = 0;
        }

        setCurrentMatch(targetIndex + 1);

        // 跳转到匹配位置
        const match = matches[targetIndex];
        const from = match.index;
        const to = from + match[0].length;

        // 先设置选择和高亮
        view.dispatch({
          selection: { anchor: from, head: to },
          effects: [
            setSearchHighlights.of({ matches, currentIndex: targetIndex })
          ]
        });

        // 强制滚动到匹配位置
        requestAnimationFrame(() => {
          // 方法1: 使用 CodeMirror 的 scrollIntoView
          view.dispatch({
            effects: EditorView.scrollIntoView(from, { y: "center", yMargin: 100 })
          });

          // 方法2: 备用滚动方法
          setTimeout(() => {
            try {
              const line = view.state.doc.lineAt(from);
              const lineElement = view.domAtPos(line.from);
              if (lineElement && lineElement.node) {
                const element = lineElement.node.nodeType === Node.TEXT_NODE
                  ? lineElement.node.parentElement
                  : lineElement.node;

                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                  });
                }
              }
            } catch (e) {
              console.log('Alternative scroll method failed:', e);
            }
          }, 50);
        });
      } else {
        setCurrentMatch(0);
        view.dispatch({
          effects: clearSearchHighlights.of(null)
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      setCurrentMatch(0);
      setTotalMatches(0);
      view.dispatch({
        effects: clearSearchHighlights.of(null)
      });
    }
  }, [caseSensitive, useRegex, editorRef]);

  // 搜索输入变化
  const handleSearchChange = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
    if (term) {
      performSearch(term);
    } else {
      setCurrentMatch(0);
      setTotalMatches(0);
      // 清除高亮
      const view = editorRef.current?.view;
      if (view) {
        view.dispatch({
          effects: clearSearchHighlights.of(null)
        });
      }
    }
  };

  // 当搜索选项变化时重新搜索
  useEffect(() => {
    if (searchTerm) {
      performSearch(searchTerm);
    }
  }, [caseSensitive, useRegex, performSearch]);

  // 键盘事件处理
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        performSearch(searchTerm, 'prev');
      } else {
        performSearch(searchTerm, 'next');
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isVisible) return null;

  return (
    <div ref={actualPanelRef} style={{
      position: 'fixed',
      top: fixedPosition.top,
      right: fixedPosition.right,
      background: 'white',
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      padding: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      zIndex: 1001, // 确保在其他元素之上
      minWidth: '280px',
      fontSize: '12px',
      maxHeight: '200px', // 限制最大高度
      overflowY: 'auto',
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '4px' }}>
        <input
          ref={searchInputRef}
          type="text"
          value={searchTerm}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          placeholder="搜索..."
          style={{
            flex: 1,
            padding: '4px 6px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: '12px'
          }}
        />
        <button
          onClick={() => performSearch(searchTerm, 'prev')}
          disabled={!searchTerm || totalMatches === 0}
          style={{
            padding: '4px 6px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            background: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
          title="上一个 (Shift+Enter)"
        >
          ↑
        </button>
        <button
          onClick={() => performSearch(searchTerm, 'next')}
          disabled={!searchTerm || totalMatches === 0}
          style={{
            padding: '4px 6px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            background: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
          title="下一个 (Enter)"
        >
          ↓
        </button>
        <button
          onClick={onClose}
          style={{
            padding: '4px 6px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            background: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
          title="关闭 (Esc)"
        >
          ✕
        </button>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '11px', color: '#666' }}>
        <label style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          <input
            type="checkbox"
            checked={caseSensitive}
            onChange={(e) => setCaseSensitive(e.target.checked)}
            style={{ margin: 0 }}
          />
          区分大小写
        </label>
        <label style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          <input
            type="checkbox"
            checked={useRegex}
            onChange={(e) => setUseRegex(e.target.checked)}
            style={{ margin: 0 }}
          />
          正则表达式
        </label>
        <span style={{ marginLeft: 'auto' }}>
          {totalMatches > 0 ? `${currentMatch}/${totalMatches}` : '无匹配'}
        </span>
      </div>
    </div>
  );
};

// 创建占位符高亮装饰器
const placeholderMark = Decoration.mark({
  class: 'cm-placeholder-mark',
  attributes: {
    style: 'color: #999; font-style: italic;'
  }
});

// 创建搜索高亮装饰器
const searchHighlightMark = Decoration.mark({
  class: 'cm-search-highlight',
  attributes: {
    style: 'background-color: yellow; color: black;'
  }
});

const currentSearchHighlightMark = Decoration.mark({
  class: 'cm-current-search-highlight',
  attributes: {
    style: 'background-color: orange; color: black; border: 1px solid #ff6600;'
  }
});

// 搜索高亮状态效果
const setSearchHighlights = StateEffect.define();
const clearSearchHighlights = StateEffect.define();

// 创建状态字段来管理搜索高亮装饰
const searchHighlightField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes);

    for (let effect of tr.effects) {
      if (effect.is(setSearchHighlights)) {
        const { matches, currentIndex } = effect.value;
        const newDecorations = [];

        matches.forEach((match, index) => {
          const from = match.index;
          const to = from + match[0].length;
          const mark = index === currentIndex ? currentSearchHighlightMark : searchHighlightMark;
          newDecorations.push(mark.range(from, to));
        });

        decorations = Decoration.set(newDecorations);
      } else if (effect.is(clearSearchHighlights)) {
        decorations = Decoration.none;
      }
    }

    return decorations;
  },
  provide: f => EditorView.decorations.from(f)
});

// 创建状态字段来管理占位符装饰
const placeholderField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes);

    // 查找所有的 {null} 和 {undefined} 占位符
    const doc = tr.state.doc;
    const newDecorations = [];

    for (let i = 1; i <= doc.lines; i++) {
      const line = doc.line(i);
      const text = line.text;

      // 查找 {null}
      const nullMatch = text.match(/\{null\}/g);

      if (nullMatch) {
        let match;

        // 查找所有匹配项
        const regex = /\{null\}/g;
        while ((match = regex.exec(text)) !== null) {
          const from = line.from + match.index;
          const to = from + match[0].length;
          newDecorations.push(placeholderMark.range(from, to));
        }
      }
    }

    return Decoration.set(newDecorations);
  },
  provide: f => EditorView.decorations.from(f)
});

/**
 * 增强的TextArea组件，使用CodeMirror实现，支持行号和防抖。
 * 注意：原有的自定义快捷键（剪切/复制行、粘贴到新行）和手动历史记录已被移除，
 * CodeMirror提供内置的撤销/重做功能。
 *
 * autoSize 属性说明：
 * - 如果传入 true，则启用自适应高度，无最小行数限制
 * - 如果传入 { minRows: number }，则启用自适应高度，并设置最小行数
 * - 如果不传入或传入 false，则使用固定高度
 */
const EnhanceTextArea = React.forwardRef((props, ref) => {
  const {
    onChange,
    value: propValue,
    defaultValue,
    debounceDelay = 300,
    showLineNumbers = true,
    style,
    autoSize,
    ...restProps
  } = props;
  const [localValue, setLocalValue] = useState(propValue ?? defaultValue ?? '');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const debounceTimerRef = useRef(null);
  const isUserInputRef = useRef(false); // To prevent external value overriding user input during debounce

  useEffect(() => {
    console.log(`[EnhanceTextArea Main] isSearchVisible state is NOW: ${isSearchVisible}`);
  }, [isSearchVisible]);
  const editorRef = useRef(null);
  const containerRef = useRef(null);

  // 解析 autoSize 配置
  const autoSizeConfig = React.useMemo(() => {
    if (!autoSize) return null;
    if (autoSize === true) return { enabled: true, minRows: 1 };
    if (typeof autoSize === 'object' && autoSize.minRows) {
      return { enabled: true, minRows: autoSize.minRows };
    }
    return null;
  }, [autoSize]);

  // Debounced onChange handler
  const debouncedOnChange = useCallback((val) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    debounceTimerRef.current = setTimeout(() => {
      if (onChange) {
        console.log(`[EnhanceTextArea] Debounced onChange triggered with value length: ${val?.length}`);
        // Create a mock event object if the parent component expects one
        const mockEvent = {
          target: {
            value: val
          }
        };
        onChange(mockEvent);
        isUserInputRef.current = false; // Reset flag after debounce completes
      }
    }, debounceDelay);
  }, [onChange, debounceDelay]);

  // Update local state when propValue changes externally, unless user is typing
  useEffect(() => {
    if (propValue !== undefined && !isUserInputRef.current && propValue !== localValue) {
      console.log('[EnhanceTextArea] External value received:', propValue?.length);
      setLocalValue(propValue);
    }
  }, [propValue]); // Removed localValue dependency to prevent loop

  // Handle CodeMirror changes
  const handleCodeMirrorChange = useCallback((val) => {
    console.log(`[EnhanceTextArea] handleCodeMirrorChange:`, {
      valueLength: val?.length,
      valuePreview: val?.substring(0, 100) + (val?.length > 100 ? '...' : ''),
      isUserInput: isUserInputRef.current,
      autoSizeEnabled: !!autoSizeConfig
    });

    isUserInputRef.current = true; // Mark as user input
    setLocalValue(val);      // Update local state immediately for responsiveness
    debouncedOnChange(val);  // Trigger debounced update for parent
  }, [debouncedOnChange, autoSizeConfig]);

  // 关闭搜索面板
  const handleCloseSearch = useCallback(() => {
    console.trace('[EnhanceTextArea Main] handleCloseSearch TRACE');
    console.log('[EnhanceTextArea Main] handleCloseSearch called. Setting isSearchVisible to false.');
    setIsSearchVisible(false);
  }, [setIsSearchVisible]); // setIsSearchVisible is stable, but good practice to include if used

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // --- CodeMirror Setup ---
  const extensions = React.useMemo(() => {
    const baseExtensions = [
      EditorView.lineWrapping, // Enable line wrapping
      placeholderField, // 添加占位符高亮
      searchHighlightField, // 添加搜索高亮
      keymap.of([
        {
          key: 'Mod-f',
          preventDefault: true,
          run: () => {
            console.log('[EnhanceTextArea Main] Mod-f pressed. Current isSearchVisible:', isSearchVisible);
            console.log('[EnhanceTextArea Main] Setting isSearchVisible to true.');
            setIsSearchVisible(true);
            console.log('[EnhanceTextArea Main] setIsSearchVisible called.');
            return true;
          }
        }
      ]), // 添加搜索快捷键
      // 通用主题设置
      EditorView.theme({
        '&': {
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '12px'
        },
        '.cm-editor': {
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '12px'
        },
        '.cm-scroller': {
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '12px'
        },
        '.cm-content': {
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          whiteSpace: 'pre-wrap',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '12px',
          // 自适应高度时的最小高度设置
          ...(autoSizeConfig && {
            minHeight: `${autoSizeConfig.minRows * 1.6}em`, // 1.6em 是行高
            padding: '8px 12px'
          })
        },
        '.cm-line': {
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '12px'
        },
        '.cm-editor.cm-focused': {
          outline: 'none'
        }
      })
    ];

    // 如果启用自适应高度，添加自适应高度的主题
    if (autoSizeConfig) {
      baseExtensions.push(
        EditorView.theme({
          '&': {
            height: 'auto'
          },
          '.cm-editor': {
            height: 'auto'
          },
          '.cm-scroller': {
            overflow: 'visible',
            height: 'auto'
          }
        })
      );
    }

    return baseExtensions;
  }, [autoSizeConfig, setIsSearchVisible]);

  if (showLineNumbers) {
    extensions.push(lineNumbers()); // Add line numbers if enabled
  }

  // --- Styling ---
  // Combine incoming style with default/necessary styles
  const editorStyle = React.useMemo(() => ({
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    overflow: autoSizeConfig ? 'visible' : 'auto', // 自适应时完全可见，不隐藏
    width: '100%', // 确保编辑器占满容器宽度
    maxWidth: '100%', // 防止超出容器
    boxSizing: 'border-box', // 确保边框包含在宽度内
    fontSize: '12px', // 默认字体大小
    // 自适应高度时不设置固定高度，让 CodeMirror 自己计算
    ...(autoSizeConfig ? {} : { height: '200px' }), // 非自适应时设置默认高度
    ...style // Allow overriding with prop style
  }), [autoSizeConfig, style]);

  return (
    <div ref={containerRef} style={{ position: 'relative', width: '100%' }}>
      <CodeMirror
        ref={(editor) => {
          editorRef.current = editor;
          if (ref) {
            if (typeof ref === 'function') {
              ref(editor);
            } else {
              ref.current = editor;
            }
          }
        }}
        value={localValue}
        onChange={handleCodeMirrorChange}
        extensions={extensions}
        style={editorStyle}
        height={autoSizeConfig ? undefined : '200px'} // 自适应模式下不设置固定高度
        basicSetup={{
          lineNumbers: showLineNumbers,
          foldGutter: false,
          dropCursor: false,
          allowMultipleSelections: false,
          indentOnInput: true,
          bracketMatching: true,
          closeBrackets: true,
          autocompletion: true,
          highlightSelectionMatches: false,
          searchKeymap: false
        }}
        // Pass down other relevant props if CodeMirror accepts them
        {...restProps} // Be cautious with spreading unknown props
        // Explicitly pass common props if needed
        readOnly={props.readOnly}
        placeholder={props.placeholder}
      />
      <SearchPanel
        isVisible={isSearchVisible}
        onClose={handleCloseSearch}
        editorRef={editorRef}
        containerRef={containerRef}
      />
    </div>
  );
});

EnhanceTextArea.displayName = 'EnhanceTextArea';

export default EnhanceTextArea;
